@echo off
echo Checking what's using port 8000...
echo.

echo === Windows processes using port 8000 ===
netstat -ano | findstr :8000
echo.

echo === WSL processes using port 8000 ===
wsl -d Ubuntu -e bash -c "sudo lsof -i:8000"
echo.

echo === Killing processes using port 8000 ===
echo Killing Windows processes...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do taskkill /PID %%a /F 2>nul

echo Killing WSL processes...
wsl -d Ubuntu -e bash -c "sudo lsof -ti:8000 | xargs -r sudo kill -9"

echo.
echo Port 8000 should now be free. Try starting ERPNext again.
pause
