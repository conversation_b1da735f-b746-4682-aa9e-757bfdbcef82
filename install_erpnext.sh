#!/bin/bash

# ERPNext Development Environment Installation Script
# This script automates the installation of ERPNext in development mode

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MARIADB_ROOT_PASSWORD="codeman1"
ADMIN_PASSWORD="admin123"
SITE_NAME="erpnext.local"
BENCH_DIR="$HOME/frappe-bench"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if service is running
service_running() {
    systemctl is-active --quiet "$1"
}

print_status "Starting ERPNext Development Environment Installation..."

# Check if running in WSL
if ! grep -q Microsoft /proc/version 2>/dev/null; then
    print_warning "This script is designed for WSL (Windows Subsystem for Linux)"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Update system packages
print_status "Updating system packages..."
sudo apt update
sudo apt install -y software-properties-common curl git build-essential

# Install system dependencies
print_status "Installing system dependencies..."
sudo apt install -y mariadb-server redis-server python3-dev python3-pip python3-venv \
    libffi-dev liblcms2-dev libldap2-dev libssl-dev libsasl2-dev libmysqlclient-dev \
    wkhtmltopdf pipx

# Install Node.js and Yarn
print_status "Installing Node.js and Yarn..."
if ! command_exists node; then
    print_error "Node.js not found. Please install Node.js first."
    exit 1
fi

if ! command_exists yarn; then
    sudo npm install -g yarn
fi

# Start and enable services
print_status "Starting database and cache services..."
sudo systemctl start mariadb redis-server
sudo systemctl enable mariadb redis-server

# Configure MariaDB
print_status "Configuring MariaDB..."
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$MARIADB_ROOT_PASSWORD'; FLUSH PRIVILEGES;" 2>/dev/null || true

# Install Frappe Bench
print_status "Installing Frappe Bench..."
pipx install frappe-bench
pipx ensurepath

# Add pipx to current session PATH
export PATH="$HOME/.local/bin:$PATH"

# Verify bench installation
if ! command_exists bench; then
    print_error "Bench installation failed. Please check the installation."
    exit 1
fi

# Initialize bench directory
print_status "Initializing Frappe Bench..."
cd "$HOME"
if [ -d "$BENCH_DIR" ]; then
    print_warning "Bench directory already exists. Removing..."
    rm -rf "$BENCH_DIR"
fi

export GIT_PYTHON_GIT_EXECUTABLE=/usr/bin/git
bench init frappe-bench --frappe-branch version-15

# Navigate to bench directory
cd "$BENCH_DIR"

# Install honcho for process management
print_status "Installing process manager..."
./env/bin/pip install honcho

# Get ERPNext app
print_status "Downloading ERPNext..."
bench get-app erpnext --branch version-15

# Create new site
print_status "Creating new site: $SITE_NAME..."
bench new-site "$SITE_NAME" --mariadb-root-password "$MARIADB_ROOT_PASSWORD" --admin-password "$ADMIN_PASSWORD"

# Install ERPNext on site
print_status "Installing ERPNext on site..."
bench --site "$SITE_NAME" install-app erpnext

# Configure development environment
print_status "Configuring development environment..."
bench --site "$SITE_NAME" set-config developer_mode 1
bench --site "$SITE_NAME" enable-scheduler

# Create symbolic link for Windows access
print_status "Creating Windows symbolic link..."
WINDOWS_PATH="/mnt/c/Users/<USER>/Desktop/erptest2/frappe-bench"
if [ -d "/mnt/c/Users/<USER>/Desktop/erptest2" ]; then
    ln -sf "$BENCH_DIR" "$WINDOWS_PATH" 2>/dev/null || true
fi

# Create startup script
print_status "Creating startup script..."
cat > "$BENCH_DIR/start_erpnext.sh" << 'EOF'
#!/bin/bash
cd ~/frappe-bench
export PATH="$HOME/.local/bin:/usr/local/bin:/usr/bin:$PATH"
export GIT_PYTHON_GIT_EXECUTABLE=/usr/bin/git

echo "Starting ERPNext development server..."
echo "Access ERPNext at: http://localhost:8000"
echo "Username: Administrator"
echo "Password: admin123"
echo ""
echo "Press Ctrl+C to stop the server"

bench serve --port 8000
EOF

chmod +x "$BENCH_DIR/start_erpnext.sh"

# Create useful aliases
print_status "Creating useful aliases..."
cat >> "$HOME/.bashrc" << 'EOF'

# ERPNext Development Aliases
alias erpnext-start='cd ~/frappe-bench && ./start_erpnext.sh'
alias erpnext-console='cd ~/frappe-bench && bench --site erpnext.local console'
alias erpnext-migrate='cd ~/frappe-bench && bench --site erpnext.local migrate'
alias erpnext-build='cd ~/frappe-bench && bench build'
alias erpnext-clear-cache='cd ~/frappe-bench && bench --site erpnext.local clear-cache'
alias erpnext-backup='cd ~/frappe-bench && bench --site erpnext.local backup'
EOF

print_success "ERPNext Development Environment Installation Complete!"
echo ""
echo "=========================================="
echo "Installation Summary:"
echo "=========================================="
echo "Site URL: http://localhost:8000"
echo "Username: Administrator"
echo "Password: $ADMIN_PASSWORD"
echo "Site Name: $SITE_NAME"
echo "Bench Directory: $BENCH_DIR"
echo ""
echo "To start ERPNext:"
echo "1. Run: cd ~/frappe-bench"
echo "2. Run: ./start_erpnext.sh"
echo "3. Open browser to: http://localhost:8000"
echo ""
echo "Useful aliases (reload shell first):"
echo "- erpnext-start: Start the development server"
echo "- erpnext-console: Open ERPNext console"
echo "- erpnext-migrate: Run database migrations"
echo "- erpnext-build: Build frontend assets"
echo "- erpnext-clear-cache: Clear application cache"
echo "- erpnext-backup: Create site backup"
echo ""
echo "For detailed documentation, see: ERPNext_Development_Guide.md"
echo "=========================================="

# Start the server
read -p "Would you like to start ERPNext now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting ERPNext development server..."
    cd "$BENCH_DIR"
    ./start_erpnext.sh
fi
