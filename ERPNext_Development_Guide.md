# ERPNext Development Environment Setup Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [System Dependencies Installation](#system-dependencies-installation)
3. [Frappe Bench Installation](#frappe-bench-installation)
4. [ERPNext Installation](#erpnext-installation)
5. [Development Environment Configuration](#development-environment-configuration)
6. [Accessing ERPNext](#accessing-erpnext)
7. [Development Commands](#development-commands)
8. [File Structure](#file-structure)
9. [Customization Guide](#customization-guide)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- Windows 10/11 with WSL2 enabled
- Ubuntu 20.04+ in WSL
- At least 4GB RAM
- 10GB free disk space
- Internet connection

### Required Software
- Python 3.8+
- Node.js 14+
- Git
- MariaDB/MySQL
- Redis
- wkhtmltopdf

## System Dependencies Installation

### 1. Update System Packages
```bash
sudo apt update
sudo apt install -y software-properties-common curl git build-essential
```

### 2. Install Database and Cache Services
```bash
sudo apt install -y mariadb-server redis-server
```

### 3. Install Python Dependencies
```bash
sudo apt install -y python3-dev python3-pip python3-venv libffi-dev liblcms2-dev libldap2-dev libssl-dev libsasl2-dev libmysqlclient-dev
```

### 4. Install Node.js and Yarn
```bash
# Install yarn globally
sudo npm install -g yarn
```

### 5. Install PDF Generation Tool
```bash
sudo apt install -y wkhtmltopdf
```

### 6. Start Services
```bash
sudo systemctl start mariadb
sudo systemctl start redis-server
sudo systemctl enable mariadb
sudo systemctl enable redis-server
```

### 7. Configure MariaDB
```bash
# Set root password
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'your_password'; FLUSH PRIVILEGES;"
```

## Frappe Bench Installation

### 1. Install Frappe Bench using pipx
```bash
# Install pipx
sudo apt install -y pipx

# Install frappe-bench
pipx install frappe-bench

# Add pipx to PATH
pipx ensurepath

# Reload shell or run:
export PATH=/home/<USER>/.local/bin:$PATH
```

### 2. Initialize Bench Directory
```bash
# Navigate to home directory
cd ~

# Initialize bench with Frappe v15
bench init frappe-bench --frappe-branch version-15

# Navigate to bench directory
cd frappe-bench
```

## ERPNext Installation

### 1. Get ERPNext App
```bash
# Download ERPNext v15
bench get-app erpnext --branch version-15
```

### 2. Create New Site
```bash
# Create site with database configuration
bench new-site your-site-name.local --mariadb-root-password your_password --admin-password admin123
```

### 3. Install ERPNext on Site
```bash
# Install ERPNext application
bench --site your-site-name.local install-app erpnext
```

## Development Environment Configuration

### 1. Enable Developer Mode
```bash
# Enable developer mode for easier customization
bench --site your-site-name.local set-config developer_mode 1
```

### 2. Enable Scheduler
```bash
# Enable background jobs
bench --site your-site-name.local enable-scheduler
```

### 3. Install Process Manager
```bash
# Install honcho for process management
./env/bin/pip install honcho
```

### 4. Create Windows Symbolic Link
```bash
# Create link to access files from Windows
ln -sf /home/<USER>/frappe-bench /mnt/c/Users/<USER>/Desktop/erptest2/frappe-bench
```

## Accessing ERPNext

### 1. Start Development Server
```bash
# Start the development server
bench serve --port 8000
```

### 2. Access Web Interface
- **URL**: `http://localhost:8000`
- **Username**: `Administrator`
- **Password**: `admin123`

### 3. Alternative Start Methods
```bash
# Start all services (if honcho is properly configured)
bench start

# Start specific services
bench serve --port 8000 &
bench worker &
bench schedule &
```

## Development Commands

### Basic Commands
```bash
# Navigate to bench directory
cd ~/frappe-bench

# Start development server
bench serve --port 8000

# Build assets
bench build

# Clear cache
bench --site your-site-name.local clear-cache

# Update database schema
bench --site your-site-name.local migrate

# Restart services
bench restart
```

### App Development
```bash
# Create new custom app
bench new-app your_custom_app

# Install app to site
bench --site your-site-name.local install-app your_custom_app

# Remove app from site
bench --site your-site-name.local uninstall-app your_custom_app
```

### Database Operations
```bash
# Backup site
bench --site your-site-name.local backup

# Restore from backup
bench --site your-site-name.local restore backup_file.sql

# Console access
bench --site your-site-name.local console

# Execute Python script
bench --site your-site-name.local execute your_script.py
```

### Site Management
```bash
# List all sites
bench --site all list-apps

# Set site as default
bench use your-site-name.local

# Create new site
bench new-site new-site.local

# Drop site
bench drop-site site-to-delete.local
```

## File Structure

```
frappe-bench/
├── apps/                    # All applications
│   ├── frappe/             # Frappe framework
│   └── erpnext/            # ERPNext application
├── sites/                  # Site-specific files
│   ├── your-site-name.local/
│   │   ├── site_config.json
│   │   ├── private/
│   │   └── public/
│   └── assets/             # Compiled assets
├── env/                    # Python virtual environment
├── config/                 # Configuration files
├── logs/                   # Log files
└── Procfile               # Process definitions
```

### Key Directories for Customization
- `apps/erpnext/erpnext/` - ERPNext core modules
- `apps/your_custom_app/` - Your custom applications
- `sites/your-site-name.local/` - Site-specific configurations

## Customization Guide

### 1. Creating Custom Apps
```bash
# Create new app
bench new-app my_custom_app

# Follow the prompts:
# App Name: my_custom_app
# App Title: My Custom App
# App Description: Custom application for specific needs
# App Publisher: Your Name
# App Email: <EMAIL>
# App Icon: fa fa-custom
# App Color: #3498db
# App License: MIT
```

### 2. Creating DocTypes (Database Tables)
```bash
# Access the web interface
# Go to: Setup > Customize > DocType > New

# Or create programmatically in your app:
# apps/my_custom_app/my_custom_app/my_custom_app/doctype/
```

### 3. Custom Scripts and Hooks
```python
# In apps/my_custom_app/my_custom_app/hooks.py
app_name = "my_custom_app"
app_title = "My Custom App"
app_publisher = "Your Name"
app_description = "Custom application"
app_icon = "octicon octicon-file-directory"
app_color = "grey"
app_email = "<EMAIL>"
app_license = "MIT"

# Document Events
doc_events = {
    "Sales Invoice": {
        "validate": "my_custom_app.custom_scripts.sales_invoice.validate_invoice"
    }
}

# Scheduled Tasks
scheduler_events = {
    "daily": [
        "my_custom_app.tasks.daily_cleanup"
    ]
}
```

### 4. Custom Fields
```python
# Add custom fields programmatically
from frappe.custom.doctype.custom_field.custom_field import create_custom_field

create_custom_field("Customer", {
    "fieldname": "custom_rating",
    "label": "Customer Rating",
    "fieldtype": "Select",
    "options": "Excellent\nGood\nAverage\nPoor",
    "insert_after": "customer_name"
})
```

### 5. Custom Reports
```python
# Create custom report in:
# apps/my_custom_app/my_custom_app/my_custom_app/report/

# Example: Sales Summary Report
# apps/my_custom_app/my_custom_app/my_custom_app/report/sales_summary/sales_summary.py

import frappe

def execute(filters=None):
    columns = [
        {"label": "Customer", "fieldname": "customer", "width": 200},
        {"label": "Total Sales", "fieldname": "total_sales", "width": 150}
    ]

    data = frappe.db.sql("""
        SELECT customer, SUM(grand_total) as total_sales
        FROM `tabSales Invoice`
        WHERE docstatus = 1
        GROUP BY customer
    """, as_dict=True)

    return columns, data
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Server Won't Start
```bash
# Check if ports are in use
sudo netstat -tulpn | grep :8000

# Kill processes using the port
sudo kill -9 $(sudo lsof -t -i:8000)

# Restart services
sudo systemctl restart mariadb redis-server
```

#### 2. Permission Issues
```bash
# Fix ownership
sudo chown -R $USER:$USER ~/frappe-bench

# Fix permissions
chmod -R 755 ~/frappe-bench
```

#### 3. Database Connection Issues
```bash
# Check MariaDB status
sudo systemctl status mariadb

# Reset MariaDB root password
sudo mysql
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
EXIT;
```

#### 4. Redis Connection Issues
```bash
# Check Redis status
sudo systemctl status redis-server

# Test Redis connection
redis-cli ping
```

#### 5. Build Errors
```bash
# Clear node modules and rebuild
rm -rf node_modules
yarn install
bench build
```

#### 6. Migration Errors
```bash
# Force migration
bench --site your-site-name.local migrate --skip-failing

# Reset migrations (use with caution)
bench --site your-site-name.local console
>>> frappe.db.sql("DELETE FROM tabPatch Log WHERE patch LIKE '%your_patch%'")
>>> frappe.db.commit()
```

### Performance Optimization

#### 1. Enable Production Mode
```bash
# Disable developer mode for better performance
bench --site your-site-name.local set-config developer_mode 0

# Enable production optimizations
bench --site your-site-name.local set-config auto_cache_clear 0
```

#### 2. Configure Caching
```bash
# Set Redis cache
bench --site your-site-name.local set-config redis_cache "redis://localhost:6379"
bench --site your-site-name.local set-config redis_queue "redis://localhost:6379"
```

#### 3. Database Optimization
```sql
-- Optimize database tables
OPTIMIZE TABLE `tabSales Invoice`;
OPTIMIZE TABLE `tabPurchase Invoice`;
```

### Backup and Restore

#### 1. Regular Backups
```bash
# Create backup
bench --site your-site-name.local backup

# Backup with files
bench --site your-site-name.local backup --with-files

# Scheduled backups (add to crontab)
0 2 * * * cd /home/<USER>/frappe-bench && bench --site your-site-name.local backup
```

#### 2. Restore Operations
```bash
# List available backups
ls sites/your-site-name.local/private/backups/

# Restore database
bench --site your-site-name.local restore /path/to/backup.sql

# Restore with files
bench --site your-site-name.local restore /path/to/backup.sql --with-public-files /path/to/files.tar --with-private-files /path/to/private-files.tar
```

## Security Considerations

### 1. Change Default Passwords
```bash
# Change administrator password
bench --site your-site-name.local set-admin-password new_secure_password
```

### 2. Configure Firewall
```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 3. SSL Configuration
```bash
# Install SSL certificate (for production)
bench setup lets-encrypt your-site-name.local
```

## Production Deployment

### 1. Setup Production Environment
```bash
# Install production dependencies
sudo apt install nginx supervisor

# Setup production configuration
sudo bench setup production $USER
```

### 2. Configure Nginx
```bash
# Enable site
sudo bench setup nginx

# Restart nginx
sudo systemctl restart nginx
```

### 3. Configure Supervisor
```bash
# Setup supervisor
sudo bench setup supervisor

# Start services
sudo supervisorctl reread
sudo supervisorctl update
```

## Additional Resources

### Documentation
- [Frappe Framework Documentation](https://frappeframework.com/docs)
- [ERPNext Documentation](https://docs.erpnext.com)
- [Frappe Developer API](https://frappeframework.com/docs/user/en/api)

### Community
- [ERPNext Forum](https://discuss.erpnext.com)
- [GitHub Repository](https://github.com/frappe/erpnext)
- [Discord Community](https://discord.gg/erpnext)

### Development Tools
- VS Code with Python extension
- Frappe/ERPNext snippets
- Database management tools (phpMyAdmin, DBeaver)

---

## Quick Reference Commands

```bash
# Start development server
bench serve --port 8000

# Create new app
bench new-app app_name

# Install app
bench --site site_name install-app app_name

# Build assets
bench build

# Clear cache
bench --site site_name clear-cache

# Migrate database
bench --site site_name migrate

# Backup site
bench --site site_name backup

# Console access
bench --site site_name console

# Update apps
bench update

# Restart bench
bench restart
```

---

**Note**: Replace `your-site-name.local`, `your_password`, and other placeholders with your actual values throughout this guide.
