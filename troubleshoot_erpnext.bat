@echo off
echo ERPNext Troubleshooting Script
echo ===============================
echo.

echo 1. Checking WSL Ubuntu accessibility...
wsl -d Ubuntu -e echo "WSL is working"
if %errorlevel% neq 0 (
    echo ERROR: WSL Ubuntu not accessible
    pause
    exit /b 1
)
echo ✓ WSL Ubuntu is accessible

echo.
echo 2. Checking frappe-bench directory...
wsl -d Ubuntu -e bash -c "test -d /home/<USER>/frappe-bench && echo 'Directory exists' || echo 'Directory missing'"

echo.
echo 3. Checking required services...
echo Checking MariaDB...
wsl -d Ubuntu -e bash -c "sudo systemctl is-active mariadb || echo 'MariaDB not running'"
echo Checking Redis...
wsl -d Ubuntu -e bash -c "sudo systemctl is-active redis-server || echo 'Redis not running'"

echo.
echo 4. Starting services if needed...
wsl -d Ubuntu -e bash -c "echo 'codeman1' | sudo -S systemctl start mariadb redis-server"
echo Services started

echo.
echo 5. Checking bench command...
wsl -d Ubuntu -e bash -c "export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:\$PATH && which bench"

echo.
echo 6. Checking site configuration...
wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && ls -la sites/"

echo.
echo 7. Testing bench command directly...
wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:\$PATH && bench --version"

echo.
echo 8. Attempting to start ERPNext with verbose output...
echo This will show detailed error messages...
echo Press Ctrl+C to stop when you see the server running message
echo.
pause

wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:\$PATH && export GIT_PYTHON_GIT_EXECUTABLE=/usr/bin/git && bench serve --port 8000 --verbose"

echo.
echo Server stopped. Check the output above for errors.
pause
