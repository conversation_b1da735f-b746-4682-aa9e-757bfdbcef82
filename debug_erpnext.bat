@echo off
echo ERPNext Debug Script
echo ==================
echo.

echo 1. Checking WSL Ubuntu status...
wsl -d Ubuntu -e echo "WSL Ubuntu is accessible"
if %errorlevel% neq 0 (
    echo ERROR: Cannot access WSL Ubuntu
    pause
    exit /b 1
)

echo 2. Checking if frappe-bench directory exists...
wsl -d Ubuntu -e bash -c "ls -la /home/<USER>/frappe-bench"
if %errorlevel% neq 0 (
    echo ERROR: frappe-bench directory not found
    pause
    exit /b 1
)

echo 3. Checking if bench command is available...
wsl -d Ubuntu -e bash -c "export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:$PATH && which bench"
if %errorlevel% neq 0 (
    echo ERROR: bench command not found
    pause
    exit /b 1
)

echo 4. Checking MariaDB and Redis services...
wsl -d Ubuntu -e bash -c "sudo systemctl is-active mariadb redis-server"

echo 5. Starting ERPNext with detailed output...
echo Press Ctrl+C to stop the server once it starts
echo.
wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:$PATH && export GIT_PYTHON_GIT_EXECUTABLE=/usr/bin/git && echo 'Starting bench serve...' && bench serve --port 8000"

echo.
echo Server stopped. Check messages above for any errors.
pause
