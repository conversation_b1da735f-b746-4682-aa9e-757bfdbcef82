@echo off
echo Simple ERPNext Startup
echo =====================
echo.

echo Starting required services...
wsl -d Ubuntu -e bash -c "echo 'codeman1' | sudo -S systemctl start mariadb redis-server"

echo.
echo Starting ERPNext server...
echo.
echo IMPORTANT: 
echo - Wait for the message "Starting development server at http://127.0.0.1:8000"
echo - Then open your browser to http://localhost:8000
echo - Username: Administrator
echo - Password: admin123
echo.
echo If you see any errors, press Ctrl+C and run troubleshoot_erpnext.bat
echo.
pause

wsl -d Ubuntu -e bash -l -c "cd /home/<USER>/frappe-bench && bench serve --port 8000"
