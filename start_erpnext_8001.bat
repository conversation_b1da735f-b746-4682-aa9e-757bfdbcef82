@echo off
echo Starting ERPNext Development Server on Port 8001...
echo.
echo This will open ERP<PERSON>ext in WSL Ubuntu
echo Access ERPNext at: http://localhost:8001
echo Username: Administrator
echo Password: admin123
echo.
echo Press Ctrl+C to stop the server when it's running
echo.
pause

echo Starting ERPNext on port 8001...
wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:$PATH && export GIT_PYTHON_GIT_EXECUTABLE=/usr/bin/git && bench serve --port 8001"

echo.
echo ERPNext server has stopped or failed to start.
echo Check the error messages above.
pause
