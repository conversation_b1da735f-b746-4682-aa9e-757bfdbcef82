# ERPNext Quick Access Guide

## 🚨 Important: ERPNext runs in WSL, not Windows directly!

Your ERPNext installation is located in **WSL Ubuntu**, not in Windows. This is why `cd ~/frappe-bench` doesn't work from Windows PowerShell.

## 🚀 How to Start ERPNext

### Option 1: Double-click the Windows Batch File (Easiest)
1. Double-click `start_erpnext.bat` in this directory
2. Press any key when prompted
3. Wait for the server to start
4. Open browser to http://localhost:8000

### Option 2: From Windows Command Line
```cmd
# From this directory (c:\Users\<USER>\Desktop\erptest2)
start_erpnext.bat
```

### Option 3: Direct WSL Command
```cmd
# From Windows PowerShell/Command Prompt
wsl -d Ubuntu -e bash -c "cd /home/<USER>/frappe-bench && ./start_erpnext.sh"
```

### Option 4: Open WSL Terminal First
```bash
# Step 1: Open WSL Ubuntu
wsl -d Ubuntu

# Step 2: Navigate to frappe-bench (now you're in Ubuntu)
cd ~/frappe-bench
# or
cd /home/<USER>/frappe-bench

# Step 3: Start ERPNext
./start_erpnext.sh
# or
bench serve --port 8000
```

## 🌐 Access ERPNext

Once the server is running:
- **URL**: http://localhost:8000
- **Username**: Administrator  
- **Password**: admin123

## 🛠️ Development Commands (Run in WSL Ubuntu)

First, always navigate to the frappe-bench directory in WSL:
```bash
# Open WSL Ubuntu
wsl -d Ubuntu

# Navigate to frappe-bench
cd ~/frappe-bench

# Then run any of these commands:
```

### Essential Commands
```bash
# Start development server
bench serve --port 8000

# Clear cache after making changes
bench --site erpnext.local clear-cache

# Build frontend assets
bench build

# Open ERPNext console for development
bench --site erpnext.local console

# Run database migrations
bench --site erpnext.local migrate

# Create backup
bench --site erpnext.local backup
```

### Creating New Apps
```bash
# Create new custom app
bench new-app my_custom_app

# Install app to site
bench --site erpnext.local install-app my_custom_app
```

## 📁 File Locations

### In WSL Ubuntu:
- **Frappe Bench**: `/home/<USER>/frappe-bench`
- **ERPNext App**: `/home/<USER>/frappe-bench/apps/erpnext`
- **Site Files**: `/home/<USER>/frappe-bench/sites/erpnext.local`

### Accessing from Windows:
- **Via File Explorer**: `\\wsl$\Ubuntu\home\nicks\frappe-bench`
- **Via VS Code**: Open WSL folder in VS Code
- **Symbolic Link**: `c:\Users\<USER>\Desktop\erptest2\frappe-bench` (if created)

## 🔧 Troubleshooting

### "bench: command not found"
This means you're not in WSL or the PATH is not set correctly.
```bash
# Make sure you're in WSL Ubuntu first
wsl -d Ubuntu

# Set the PATH
export PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:$PATH

# Navigate to frappe-bench
cd ~/frappe-bench
```

### "No such file or directory"
Make sure you're using the correct paths:
```bash
# Wrong (from Windows)
cd ~/frappe-bench  # This won't work from Windows

# Correct (from WSL Ubuntu)
wsl -d Ubuntu
cd ~/frappe-bench  # This works in WSL
```

### Server Won't Start
```bash
# Check if services are running
sudo systemctl status mariadb redis-server

# Start services if needed
sudo systemctl start mariadb redis-server

# Check if port 8000 is in use
sudo netstat -tulpn | grep :8000
```

## 💡 Pro Tips

1. **Use VS Code with WSL Extension**: Open your frappe-bench folder directly in VS Code using the WSL extension for the best development experience.

2. **Create Aliases**: Add these to your `~/.bashrc` in WSL:
   ```bash
   alias erpnext-start='cd ~/frappe-bench && bench serve --port 8000'
   alias erpnext-console='cd ~/frappe-bench && bench --site erpnext.local console'
   alias erpnext-clear='cd ~/frappe-bench && bench --site erpnext.local clear-cache'
   ```

3. **Bookmark the URL**: Save http://localhost:8000 in your browser bookmarks.

4. **Keep WSL Terminal Open**: Keep a WSL Ubuntu terminal open and navigate to frappe-bench once, then use it for all development commands.

## 📚 Next Steps

1. **Start ERPNext**: Use one of the methods above
2. **Login**: Go to http://localhost:8000 and login
3. **Explore**: Familiarize yourself with the ERPNext interface
4. **Read the Development Guide**: Check `ERPNext_Development_Tutorial.md`
5. **Start Developing**: Create your first custom app!

---

**Remember**: Always work within WSL Ubuntu when developing with ERPNext! 🐧
