# ERPNext Development Tutorial

A comprehensive guide to developing custom applications and features in your ERPNext development environment.

## Table of Contents
1. [Getting Started with Development](#getting-started-with-development)
2. [Understanding ERPNext Architecture](#understanding-erpnext-architecture)
3. [Creating Your First Custom App](#creating-your-first-custom-app)
4. [Working with DocTypes](#working-with-doctypes)
5. [Custom Scripts and Server Scripts](#custom-scripts-and-server-scripts)
6. [Creating Custom Reports](#creating-custom-reports)
7. [Building Web Pages](#building-web-pages)
8. [API Development](#api-development)
9. [Frontend Customization](#frontend-customization)
10. [Testing and Debugging](#testing-and-debugging)

## Getting Started with Development

### Prerequisites
- ERPNext development environment running (from our previous setup)
- Basic knowledge of Python, JavaScript, HTML, CSS
- Understanding of database concepts
- Familiarity with Git version control

### Development Environment Overview
```
Your Setup:
- ERPNext URL: http://localhost:8000
- Admin User: Administrator
- Password: admin123
- Bench Directory: ~/frappe-bench
- Developer Mode: Enabled ✅
```

### Essential Development Commands
# MUST ACCESS THROUGH WSL TERMINAL
```bash
# Navigate to bench directory
cd ~/frappe-bench

# Start development server
bench serve --port 8000

# Open ERPNext console
bench --site erpnext.local console

# Clear cache after changes
bench --site erpnext.local clear-cache

# Build frontend assets
bench build

# Watch for changes and auto-build
bench watch
```

## Understanding ERPNext Architecture

### Framework Structure
```
ERPNext is built on Frappe Framework:
┌─ Frappe Framework (Core)
│  ├─ Database ORM
│  ├─ Web Framework
│  ├─ Authentication
│  └─ Background Jobs
└─ ERPNext (Application)
   ├─ Accounting
   ├─ CRM
   ├─ Manufacturing
   └─ HR
```

### Key Concepts

#### 1. DocTypes
- Database tables with metadata
- Define structure, permissions, and behavior
- Examples: Customer, Sales Invoice, Item

#### 2. Documents
- Individual records in DocTypes
- Like rows in a database table
- Have states: Draft, Submitted, Cancelled

#### 3. Apps
- Modular applications
- Contain DocTypes, reports, pages
- Can be installed/uninstalled

#### 4. Hooks
- Event-driven programming
- Execute code on document events
- Examples: before_save, after_insert

## Creating Your First Custom App

### Step 1: Create the App
```bash
cd ~/frappe-bench
bench new-app library_management

# Follow the prompts:
# App Name: library_management
# App Title: Library Management
# App Description: A simple library management system
# App Publisher: Your Name
# App Email: <EMAIL>
# App Icon: fa fa-book
# App Color: #3498db
# App License: MIT
```

### Step 2: Install the App
```bash
# Install app to your site
bench --site erpnext.local install-app library_management

# Restart to load the app
bench restart
```

### Step 3: Verify Installation
1. Go to http://localhost:8000
2. Login as Administrator
3. Go to "Desk" → You should see your app in the modules

## Working with DocTypes

### Creating a DocType via Web Interface

#### Example: Creating a "Library Member" DocType

1. **Navigate to DocType List**
   - Go to: Setup → Customize → DocType → New

2. **Basic Information**
   ```
   Module: Library Management
   Name: Library Member
   Naming: field:member_id
   ```

3. **Add Fields**
   ```
   Field 1:
   - Type: Data
   - Label: Member ID
   - Name: member_id
   - Reqd: Yes
   - Unique: Yes

   Field 2:
   - Type: Data
   - Label: Full Name
   - Name: full_name
   - Reqd: Yes

   Field 3:
   - Type: Data
   - Label: Email
   - Name: email
   - Options: Email

   Field 4:
   - Type: Data
   - Label: Phone
   - Name: phone

   Field 5:
   - Type: Select
   - Label: Membership Type
   - Name: membership_type
   - Options: 
     Student
     Faculty
     Staff
     Public

   Field 6:
   - Type: Date
   - Label: Join Date
   - Name: join_date
   - Default: Today
   ```

4. **Permissions**
   - Add permissions for different roles
   - System Manager: All permissions
   - Librarian: Read, Write, Create

5. **Save the DocType**

### Creating DocType Programmatically

Create file: `apps/library_management/library_management/library_management/doctype/library_book/library_book.json`

```json
{
 "actions": [],
 "allow_rename": 1,
 "creation": "2024-01-01 00:00:00.000000",
 "doctype": "DocType",
 "editable_grid": 1,
 "engine": "InnoDB",
 "field_order": [
  "isbn",
  "title",
  "author",
  "publisher",
  "publication_year",
  "category",
  "status"
 ],
 "fields": [
  {
   "fieldname": "isbn",
   "fieldtype": "Data",
   "label": "ISBN",
   "reqd": 1,
   "unique": 1
  },
  {
   "fieldname": "title",
   "fieldtype": "Data",
   "label": "Title",
   "reqd": 1
  },
  {
   "fieldname": "author",
   "fieldtype": "Data",
   "label": "Author",
   "reqd": 1
  },
  {
   "fieldname": "publisher",
   "fieldtype": "Data",
   "label": "Publisher"
  },
  {
   "fieldname": "publication_year",
   "fieldtype": "Int",
   "label": "Publication Year"
  },
  {
   "fieldname": "category",
   "fieldtype": "Select",
   "label": "Category",
   "options": "Fiction\nNon-Fiction\nScience\nHistory\nBiography"
  },
  {
   "fieldname": "status",
   "fieldtype": "Select",
   "label": "Status",
   "options": "Available\nIssued\nMaintenance",
   "default": "Available"
  }
 ],
 "modified": "2024-01-01 00:00:00.000000",
 "modified_by": "Administrator",
 "module": "Library Management",
 "name": "Library Book",
 "owner": "Administrator",
 "permissions": [
  {
   "create": 1,
   "delete": 1,
   "email": 1,
   "export": 1,
   "print": 1,
   "read": 1,
   "report": 1,
   "role": "System Manager",
   "share": 1,
   "write": 1
  }
 ],
 "sort_field": "modified",
 "sort_order": "DESC",
 "track_changes": 1
}
```

### Adding Server-Side Logic

Create file: `apps/library_management/library_management/library_management/doctype/library_book/library_book.py`

```python
import frappe
from frappe.model.document import Document

class LibraryBook(Document):
    def validate(self):
        """Validate the document before saving"""
        self.validate_isbn()
        self.set_title_case()
    
    def validate_isbn(self):
        """Validate ISBN format"""
        if self.isbn:
            # Remove any spaces or hyphens
            isbn = self.isbn.replace('-', '').replace(' ', '')
            
            # Check if ISBN-10 or ISBN-13
            if len(isbn) not in [10, 13]:
                frappe.throw("ISBN must be 10 or 13 digits")
            
            # Check if all characters are digits (except last char in ISBN-10 can be X)
            if len(isbn) == 10:
                if not (isbn[:-1].isdigit() and (isbn[-1].isdigit() or isbn[-1].upper() == 'X')):
                    frappe.throw("Invalid ISBN-10 format")
            elif len(isbn) == 13:
                if not isbn.isdigit():
                    frappe.throw("Invalid ISBN-13 format")
    
    def set_title_case(self):
        """Convert title to title case"""
        if self.title:
            self.title = self.title.title()
    
    def before_save(self):
        """Execute before saving the document"""
        self.update_availability_status()
    
    def update_availability_status(self):
        """Update status based on current issues"""
        # Check if book is currently issued
        issued = frappe.db.exists("Library Transaction", {
            "book": self.name,
            "transaction_type": "Issue",
            "returned": 0
        })
        
        if issued and self.status == "Available":
            self.status = "Issued"
        elif not issued and self.status == "Issued":
            self.status = "Available"
```

## Custom Scripts and Server Scripts

### Client Scripts (JavaScript)
Add client-side behavior to forms.

#### Example: Auto-generate Member ID
1. Go to: Setup → Customize → Client Script → New
2. Set:
   ```
   DocType: Library Member
   Type: Form
   ```
3. Script:
   ```javascript
   frappe.ui.form.on('Library Member', {
       refresh: function(frm) {
           // Auto-generate member ID if not set
           if (frm.doc.__islocal && !frm.doc.member_id) {
               frappe.call({
                   method: 'library_management.api.generate_member_id',
                   callback: function(r) {
                       if (r.message) {
                           frm.set_value('member_id', r.message);
                       }
                   }
               });
           }
       },
       
       membership_type: function(frm) {
           // Set different colors based on membership type
           if (frm.doc.membership_type === 'Student') {
               frm.set_df_property('membership_type', 'description', 
                   'Student members get 30-day loan period');
           } else if (frm.doc.membership_type === 'Faculty') {
               frm.set_df_property('membership_type', 'description', 
                   'Faculty members get 60-day loan period');
           }
       }
   });
   ```

### Server Scripts (Python)
Execute server-side logic on document events.

#### Example: Send Welcome Email
1. Go to: Setup → Customize → Server Script → New
2. Set:
   ```
   DocType: Library Member
   Event: After Insert
   ```
3. Script:
   ```python
   # Send welcome email to new library member
   frappe.sendmail(
       recipients=[doc.email],
       subject="Welcome to Our Library!",
       message=f"""
       Dear {doc.full_name},
       
       Welcome to our library! Your member ID is: {doc.member_id}
       
       You can now borrow books and access our digital resources.
       
       Best regards,
       Library Team
       """
   )
   
   # Log the activity
   frappe.log_error(f"Welcome email sent to {doc.email}", "Library Member Registration")

## Creating Custom Reports

### Query Report Example: Library Statistics

1. **Create Report Directory**
   ```bash
   mkdir -p apps/library_management/library_management/library_management/report/library_statistics
   ```

2. **Create Report JSON**
   File: `library_statistics.json`
   ```json
   {
    "add_total_row": 1,
    "creation": "2024-01-01 00:00:00.000000",
    "disable_prepared_report": 0,
    "disabled": 0,
    "doctype": "Report",
    "is_standard": "Yes",
    "modified": "2024-01-01 00:00:00.000000",
    "module": "Library Management",
    "name": "Library Statistics",
    "owner": "Administrator",
    "prepared_report": 0,
    "ref_doctype": "Library Book",
    "report_name": "Library Statistics",
    "report_type": "Query Report",
    "roles": [
     {
      "role": "System Manager"
     },
     {
      "role": "Librarian"
     }
    ]
   }
   ```

3. **Create Report Python File**
   File: `library_statistics.py`
   ```python
   import frappe
   from frappe import _

   def execute(filters=None):
       columns = get_columns()
       data = get_data(filters)
       chart = get_chart_data(data)

       return columns, data, None, chart

   def get_columns():
       return [
           {
               "label": _("Category"),
               "fieldname": "category",
               "fieldtype": "Data",
               "width": 150
           },
           {
               "label": _("Total Books"),
               "fieldname": "total_books",
               "fieldtype": "Int",
               "width": 120
           },
           {
               "label": _("Available"),
               "fieldname": "available",
               "fieldtype": "Int",
               "width": 120
           },
           {
               "label": _("Issued"),
               "fieldname": "issued",
               "fieldtype": "Int",
               "width": 120
           },
           {
               "label": _("Availability %"),
               "fieldname": "availability_percent",
               "fieldtype": "Percent",
               "width": 120
           }
       ]

   def get_data(filters):
       conditions = ""
       if filters.get("category"):
           conditions = f"WHERE category = '{filters.get('category')}'"

       data = frappe.db.sql(f"""
           SELECT
               category,
               COUNT(*) as total_books,
               SUM(CASE WHEN status = 'Available' THEN 1 ELSE 0 END) as available,
               SUM(CASE WHEN status = 'Issued' THEN 1 ELSE 0 END) as issued,
               (SUM(CASE WHEN status = 'Available' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as availability_percent
           FROM `tabLibrary Book`
           {conditions}
           GROUP BY category
           ORDER BY category
       """, as_dict=True)

       return data

   def get_chart_data(data):
       categories = [d.category for d in data]
       available = [d.available for d in data]
       issued = [d.issued for d in data]

       return {
           "data": {
               "labels": categories,
               "datasets": [
                   {
                       "name": "Available",
                       "values": available
                   },
                   {
                       "name": "Issued",
                       "values": issued
                   }
               ]
           },
           "type": "bar",
           "height": 300
       }
   ```

4. **Create Report JavaScript**
   File: `library_statistics.js`
   ```javascript
   frappe.query_reports["Library Statistics"] = {
       "filters": [
           {
               "fieldname": "category",
               "label": __("Category"),
               "fieldtype": "Select",
               "options": "\nFiction\nNon-Fiction\nScience\nHistory\nBiography"
           }
       ]
   };
   ```

### Script Report Example: Overdue Books

File: `apps/library_management/library_management/library_management/report/overdue_books/overdue_books.py`

```python
import frappe
from frappe import _
from datetime import datetime, timedelta

def execute(filters=None):
    columns = [
        {
            "label": _("Member ID"),
            "fieldname": "member_id",
            "fieldtype": "Link",
            "options": "Library Member",
            "width": 120
        },
        {
            "label": _("Member Name"),
            "fieldname": "member_name",
            "fieldtype": "Data",
            "width": 150
        },
        {
            "label": _("Book"),
            "fieldname": "book",
            "fieldtype": "Link",
            "options": "Library Book",
            "width": 120
        },
        {
            "label": _("Book Title"),
            "fieldname": "book_title",
            "fieldtype": "Data",
            "width": 200
        },
        {
            "label": _("Issue Date"),
            "fieldname": "issue_date",
            "fieldtype": "Date",
            "width": 100
        },
        {
            "label": _("Due Date"),
            "fieldname": "due_date",
            "fieldtype": "Date",
            "width": 100
        },
        {
            "label": _("Days Overdue"),
            "fieldname": "days_overdue",
            "fieldtype": "Int",
            "width": 120
        },
        {
            "label": _("Fine Amount"),
            "fieldname": "fine_amount",
            "fieldtype": "Currency",
            "width": 120
        }
    ]

    # Get overdue transactions
    overdue_data = frappe.db.sql("""
        SELECT
            lt.member,
            lm.full_name as member_name,
            lt.book,
            lb.title as book_title,
            lt.issue_date,
            lt.due_date,
            DATEDIFF(CURDATE(), lt.due_date) as days_overdue
        FROM `tabLibrary Transaction` lt
        JOIN `tabLibrary Member` lm ON lt.member = lm.name
        JOIN `tabLibrary Book` lb ON lt.book = lb.name
        WHERE lt.transaction_type = 'Issue'
        AND lt.returned = 0
        AND lt.due_date < CURDATE()
        ORDER BY lt.due_date
    """, as_dict=True)

    # Calculate fine (₹2 per day)
    for row in overdue_data:
        row['fine_amount'] = row['days_overdue'] * 2
        row['member_id'] = row['member']

    return columns, overdue_data

## Building Web Pages

### Creating a Public Library Catalog

1. **Create Web Page**
   File: `apps/library_management/library_management/library_management/web_template/library_catalog.html`
   ```html
   <div class="container mt-4">
       <h1>Library Catalog</h1>

       <!-- Search Form -->
       <div class="row mb-4">
           <div class="col-md-8">
               <input type="text" id="search-books" class="form-control"
                      placeholder="Search books by title, author, or ISBN...">
           </div>
           <div class="col-md-4">
               <select id="category-filter" class="form-control">
                   <option value="">All Categories</option>
                   <option value="Fiction">Fiction</option>
                   <option value="Non-Fiction">Non-Fiction</option>
                   <option value="Science">Science</option>
                   <option value="History">History</option>
                   <option value="Biography">Biography</option>
               </select>
           </div>
       </div>

       <!-- Books Grid -->
       <div id="books-container" class="row">
           <!-- Books will be loaded here -->
       </div>

       <!-- Pagination -->
       <nav aria-label="Page navigation">
           <ul class="pagination justify-content-center" id="pagination">
           </ul>
       </nav>
   </div>

   <script>
   $(document).ready(function() {
       let currentPage = 1;
       const booksPerPage = 12;

       function loadBooks(page = 1, search = '', category = '') {
           frappe.call({
               method: 'library_management.api.get_books',
               args: {
                   page: page,
                   limit: booksPerPage,
                   search: search,
                   category: category
               },
               callback: function(r) {
                   if (r.message) {
                       displayBooks(r.message.books);
                       displayPagination(r.message.total_pages, page);
                   }
               }
           });
       }

       function displayBooks(books) {
           let html = '';
           books.forEach(function(book) {
               html += `
                   <div class="col-md-4 mb-4">
                       <div class="card">
                           <div class="card-body">
                               <h5 class="card-title">${book.title}</h5>
                               <p class="card-text">
                                   <strong>Author:</strong> ${book.author}<br>
                                   <strong>Category:</strong> ${book.category}<br>
                                   <strong>Status:</strong>
                                   <span class="badge ${book.status === 'Available' ? 'badge-success' : 'badge-warning'}">
                                       ${book.status}
                                   </span>
                               </p>
                               <small class="text-muted">ISBN: ${book.isbn}</small>
                           </div>
                       </div>
                   </div>
               `;
           });
           $('#books-container').html(html);
       }

       function displayPagination(totalPages, currentPage) {
           let html = '';
           for (let i = 1; i <= totalPages; i++) {
               html += `
                   <li class="page-item ${i === currentPage ? 'active' : ''}">
                       <a class="page-link" href="#" data-page="${i}">${i}</a>
                   </li>
               `;
           }
           $('#pagination').html(html);
       }

       // Event handlers
       $('#search-books').on('input', function() {
           const search = $(this).val();
           const category = $('#category-filter').val();
           loadBooks(1, search, category);
       });

       $('#category-filter').on('change', function() {
           const category = $(this).val();
           const search = $('#search-books').val();
           loadBooks(1, search, category);
       });

       $(document).on('click', '.page-link', function(e) {
           e.preventDefault();
           const page = parseInt($(this).data('page'));
           const search = $('#search-books').val();
           const category = $('#category-filter').val();
           loadBooks(page, search, category);
       });

       // Initial load
       loadBooks();
   });
   </script>
   ```

2. **Create API Methods**
   File: `apps/library_management/library_management/api.py`
   ```python
   import frappe
   import math

   @frappe.whitelist(allow_guest=True)
   def get_books(page=1, limit=12, search='', category=''):
       """Get books for public catalog"""
       page = int(page)
       limit = int(limit)
       offset = (page - 1) * limit

       conditions = []
       if search:
           conditions.append(f"(title LIKE '%{search}%' OR author LIKE '%{search}%' OR isbn LIKE '%{search}%')")
       if category:
           conditions.append(f"category = '{category}'")

       where_clause = ""
       if conditions:
           where_clause = "WHERE " + " AND ".join(conditions)

       # Get total count
       total_count = frappe.db.sql(f"""
           SELECT COUNT(*) as count
           FROM `tabLibrary Book`
           {where_clause}
       """)[0][0]

       # Get books
       books = frappe.db.sql(f"""
           SELECT name, title, author, isbn, category, status
           FROM `tabLibrary Book`
           {where_clause}
           ORDER BY title
           LIMIT {limit} OFFSET {offset}
       """, as_dict=True)

       total_pages = math.ceil(total_count / limit)

       return {
           'books': books,
           'total_pages': total_pages,
           'current_page': page,
           'total_count': total_count
       }

   @frappe.whitelist()
   def generate_member_id():
       """Generate unique member ID"""
       # Get the last member ID
       last_member = frappe.db.sql("""
           SELECT member_id
           FROM `tabLibrary Member`
           WHERE member_id REGEXP '^LM[0-9]+$'
           ORDER BY CAST(SUBSTRING(member_id, 3) AS UNSIGNED) DESC
           LIMIT 1
       """)

       if last_member:
           last_number = int(last_member[0][0][2:])  # Remove 'LM' prefix
           new_number = last_number + 1
       else:
           new_number = 1

       return f"LM{new_number:04d}"  # Format as LM0001, LM0002, etc.

## API Development

### REST API Endpoints

ERPNext automatically creates REST API endpoints for all DocTypes. You can also create custom API methods.

#### Built-in API Endpoints
```bash
# Get all Library Books
GET /api/resource/Library Book

# Get specific Library Book
GET /api/resource/Library Book/{name}

# Create new Library Book
POST /api/resource/Library Book

# Update Library Book
PUT /api/resource/Library Book/{name}

# Delete Library Book
DELETE /api/resource/Library Book/{name}
```

#### Custom API Methods

File: `apps/library_management/library_management/api.py`

```python
import frappe
from frappe import _

@frappe.whitelist()
def issue_book(member, book, due_date):
    """Issue a book to a member"""

    # Validate member exists and is active
    member_doc = frappe.get_doc("Library Member", member)
    if not member_doc:
        frappe.throw(_("Member not found"))

    # Validate book exists and is available
    book_doc = frappe.get_doc("Library Book", book)
    if book_doc.status != "Available":
        frappe.throw(_("Book is not available"))

    # Check if member has overdue books
    overdue_books = frappe.db.count("Library Transaction", {
        "member": member,
        "transaction_type": "Issue",
        "returned": 0,
        "due_date": ["<", frappe.utils.today()]
    })

    if overdue_books > 0:
        frappe.throw(_("Member has overdue books. Please return them first."))

    # Create transaction
    transaction = frappe.get_doc({
        "doctype": "Library Transaction",
        "member": member,
        "book": book,
        "transaction_type": "Issue",
        "issue_date": frappe.utils.today(),
        "due_date": due_date,
        "returned": 0
    })
    transaction.insert()

    # Update book status
    book_doc.status = "Issued"
    book_doc.save()

    return {
        "success": True,
        "message": _("Book issued successfully"),
        "transaction": transaction.name
    }

@frappe.whitelist()
def return_book(transaction_id):
    """Return a book"""

    transaction = frappe.get_doc("Library Transaction", transaction_id)

    if transaction.returned:
        frappe.throw(_("Book already returned"))

    # Update transaction
    transaction.returned = 1
    transaction.return_date = frappe.utils.today()
    transaction.save()

    # Update book status
    book = frappe.get_doc("Library Book", transaction.book)
    book.status = "Available"
    book.save()

    # Calculate fine if overdue
    fine_amount = 0
    if frappe.utils.getdate(transaction.return_date) > frappe.utils.getdate(transaction.due_date):
        days_overdue = (frappe.utils.getdate(transaction.return_date) - frappe.utils.getdate(transaction.due_date)).days
        fine_amount = days_overdue * 2  # ₹2 per day

    return {
        "success": True,
        "message": _("Book returned successfully"),
        "fine_amount": fine_amount
    }

@frappe.whitelist()
def get_member_books(member):
    """Get all books currently issued to a member"""

    books = frappe.db.sql("""
        SELECT
            lt.name as transaction_id,
            lt.book,
            lb.title,
            lb.author,
            lt.issue_date,
            lt.due_date,
            CASE
                WHEN lt.due_date < CURDATE() THEN 'Overdue'
                WHEN DATEDIFF(lt.due_date, CURDATE()) <= 3 THEN 'Due Soon'
                ELSE 'Active'
            END as status
        FROM `tabLibrary Transaction` lt
        JOIN `tabLibrary Book` lb ON lt.book = lb.name
        WHERE lt.member = %s
        AND lt.transaction_type = 'Issue'
        AND lt.returned = 0
        ORDER BY lt.due_date
    """, (member,), as_dict=True)

    return books

@frappe.whitelist(allow_guest=True)
def search_books(query, category=None, limit=20):
    """Search books in the catalog"""

    conditions = ["(title LIKE %(query)s OR author LIKE %(query)s OR isbn LIKE %(query)s)"]
    values = {"query": f"%{query}%"}

    if category:
        conditions.append("category = %(category)s")
        values["category"] = category

    where_clause = " AND ".join(conditions)

    books = frappe.db.sql(f"""
        SELECT name, title, author, isbn, category, status, publisher, publication_year
        FROM `tabLibrary Book`
        WHERE {where_clause}
        ORDER BY title
        LIMIT %(limit)s
    """, {**values, "limit": limit}, as_dict=True)

    return books
```

#### API Authentication

For protected endpoints, include API key and secret in headers:
```bash
# Get API credentials from User settings
curl -X GET "http://localhost:8000/api/resource/Library Book" \
  -H "Authorization: token api_key:api_secret"
```

## Frontend Customization

### Custom CSS and JavaScript

#### Global Customization
1. Go to: Setup → Customize → Custom CSS
2. Add your custom styles:

```css
/* Custom Library Management Styles */
.library-card {
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.book-available {
    color: #27ae60;
    font-weight: bold;
}

.book-issued {
    color: #e74c3c;
    font-weight: bold;
}

.overdue-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

/* Custom form styling */
.frappe-form .library-member-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
}

.frappe-form .library-member-form .form-control {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 4px;
}
```

#### DocType-specific JavaScript
File: `apps/library_management/library_management/library_management/doctype/library_member/library_member.js`

```javascript
frappe.ui.form.on('Library Member', {
    refresh: function(frm) {
        // Add custom buttons
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__('View Books'), function() {
                frappe.route_options = {"member": frm.doc.name};
                frappe.set_route("query-report", "Member Books");
            });

            frm.add_custom_button(__('Issue Book'), function() {
                show_issue_book_dialog(frm);
            }, __('Actions'));

            frm.add_custom_button(__('Payment History'), function() {
                frappe.route_options = {"member": frm.doc.name};
                frappe.set_route("List", "Library Payment");
            }, __('Actions'));
        }

        // Add member statistics
        if (!frm.doc.__islocal) {
            show_member_statistics(frm);
        }
    },

    membership_type: function(frm) {
        // Set loan period based on membership type
        const loan_periods = {
            'Student': 30,
            'Faculty': 60,
            'Staff': 45,
            'Public': 14
        };

        if (frm.doc.membership_type && loan_periods[frm.doc.membership_type]) {
            frm.set_df_property('loan_period', 'default', loan_periods[frm.doc.membership_type]);
            if (frm.doc.__islocal) {
                frm.set_value('loan_period', loan_periods[frm.doc.membership_type]);
            }
        }
    }
});

function show_issue_book_dialog(frm) {
    let dialog = new frappe.ui.Dialog({
        title: __('Issue Book'),
        fields: [
            {
                fieldtype: 'Link',
                fieldname: 'book',
                label: __('Book'),
                options: 'Library Book',
                reqd: 1,
                get_query: function() {
                    return {
                        filters: {
                            'status': 'Available'
                        }
                    };
                }
            },
            {
                fieldtype: 'Date',
                fieldname: 'due_date',
                label: __('Due Date'),
                reqd: 1,
                default: frappe.datetime.add_days(frappe.datetime.get_today(), 30)
            }
        ],
        primary_action: function(values) {
            frappe.call({
                method: 'library_management.api.issue_book',
                args: {
                    member: frm.doc.name,
                    book: values.book,
                    due_date: values.due_date
                },
                callback: function(r) {
                    if (r.message && r.message.success) {
                        frappe.msgprint(r.message.message);
                        dialog.hide();
                        frm.reload_doc();
                    }
                }
            });
        },
        primary_action_label: __('Issue Book')
    });

    dialog.show();
}

function show_member_statistics(frm) {
    frappe.call({
        method: 'library_management.api.get_member_statistics',
        args: {
            member: frm.doc.name
        },
        callback: function(r) {
            if (r.message) {
                const stats = r.message;
                const html = `
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-primary">${stats.total_books_issued}</h4>
                                    <p>Total Books Issued</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-success">${stats.books_returned}</h4>
                                    <p>Books Returned</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-warning">${stats.current_books}</h4>
                                    <p>Current Books</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-danger">${stats.overdue_books}</h4>
                                    <p>Overdue Books</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                frm.dashboard.add_section(html, __('Member Statistics'));
            }
        }
    });
}
```

## Testing and Debugging

### Unit Testing

#### Create Test Files
File: `apps/library_management/library_management/library_management/doctype/library_book/test_library_book.py`

```python
import unittest
import frappe
from frappe.tests.utils import FrappeTestCase

class TestLibraryBook(FrappeTestCase):
    def setUp(self):
        """Set up test data"""
        self.test_book_data = {
            "doctype": "Library Book",
            "isbn": "9780123456789",
            "title": "test book title",
            "author": "Test Author",
            "category": "Fiction",
            "status": "Available"
        }

    def test_book_creation(self):
        """Test basic book creation"""
        book = frappe.get_doc(self.test_book_data)
        book.insert()

        # Check if book was created
        self.assertTrue(frappe.db.exists("Library Book", book.name))

        # Check if title was converted to title case
        self.assertEqual(book.title, "Test Book Title")

        # Clean up
        book.delete()

    def test_isbn_validation(self):
        """Test ISBN validation"""
        # Test invalid ISBN
        invalid_book = frappe.get_doc(self.test_book_data)
        invalid_book.isbn = "123"  # Invalid ISBN

        with self.assertRaises(frappe.ValidationError):
            invalid_book.insert()
```

#### Running Tests
```bash
# Run all tests for the app
bench --site erpnext.local run-tests library_management

# Run specific test
bench --site erpnext.local run-tests library_management.library_management.doctype.library_book.test_library_book

# Run tests with coverage
bench --site erpnext.local run-tests library_management --coverage
```

### Debugging Techniques

#### 1. Using Console
```bash
# Open ERPNext console
bench --site erpnext.local console

# In console:
>>> doc = frappe.get_doc("Library Book", "BOOK-001")
>>> print(doc.as_dict())
>>> doc.status = "Available"
>>> doc.save()
```

#### 2. Debug Logging
```python
# Add to your Python code
import frappe

# Log debug information
frappe.log_error("Debug message", "Library Management Debug")

# Log with different levels
frappe.logger().debug("Debug message")
frappe.logger().info("Info message")
frappe.logger().warning("Warning message")
frappe.logger().error("Error message")
```

### Best Practices

#### 1. Code Organization
- Keep related functionality in the same app
- Use meaningful names for DocTypes and fields
- Follow Python PEP 8 style guidelines
- Document your code with docstrings

#### 2. Security
- Always validate user input
- Use frappe.whitelist() for API methods
- Implement proper permissions
- Sanitize data before database operations

#### 3. Performance
- Use database indexes on frequently queried fields
- Implement caching for expensive operations
- Use bulk operations for large datasets
- Optimize database queries

## Conclusion

This tutorial covered the essential aspects of ERPNext development:

1. **Architecture Understanding** - How ERPNext and Frappe work together
2. **Custom App Creation** - Building your own applications
3. **DocType Development** - Creating and customizing data models
4. **Scripting** - Adding business logic with Python and JavaScript
5. **Reporting** - Building custom reports and analytics
6. **API Development** - Creating REST APIs and integrations
7. **Frontend Customization** - Enhancing user interfaces
8. **Testing & Debugging** - Ensuring code quality and reliability

### Next Steps

1. **Practice** - Build more complex applications
2. **Explore** - Study existing ERPNext modules for inspiration
3. **Contribute** - Participate in the ERPNext community
4. **Learn** - Stay updated with Frappe framework developments

### Resources

- [Frappe Framework Documentation](https://frappeframework.com/docs)
- [ERPNext Developer Guide](https://docs.erpnext.com/docs/user/manual/en/customize-erpnext)
- [Community Forum](https://discuss.erpnext.com)
- [GitHub Repository](https://github.com/frappe/erpnext)

Happy coding! 🚀
